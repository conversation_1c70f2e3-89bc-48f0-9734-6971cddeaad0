import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
// 通用字体
import 'vfonts/Lato.css'
import router from './router';
import axios from 'axios';
import naive from 'naive-ui'
const api = axios.create({
    baseURL: 'http://127.0.0.1:5150',
    // baseURL: 'http://47.116.8.252:5150',
});
// 等宽字体
import 'vfonts/FiraCode.css'
const app = createApp(App)
app.provide('$api', api);
app.use(naive)
app.use(router)
app.mount('#app')
