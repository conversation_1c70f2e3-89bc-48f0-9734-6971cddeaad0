<template>
    <div class="container">
        <div>
            <h2>
                配置列表
                <n-button type="primary" @click="addNewRow" style="margin-left: 20px;">添加配置</n-button>
            </h2>
        </div>
        <div class="contentTable">
            <n-data-table 
                :columns="columns" 
                :data="dataList"
                :scroll-x="800" 
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton, NInput } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';

const $api = inject('$api') as AxiosInstance

// 配置项接口
interface ConfigItem {
    id?: number
    name: string
    desc: string
    isEditing?: boolean
    originalName?: string
    originaldesc?: string
}

// 数据列表
let dataList: ConfigItem[] = reactive([])

// 表格列定义
let columns = ref([
    {
        title: 'ID',
        key: 'id',
        width: 80
    },
    {
        title: '名称',
        key: 'name',
        width: 200,
        render(row: ConfigItem) {
            if (row.isEditing) {
                return h(NInput, {
                    value: row.name,
                    onUpdateValue: (value: string) => {
                        row.name = value
                    },
                    placeholder: '请输入名称'
                })
            }
            return row.name
        }
    },
    {
        title: '配置详情',
        key: 'desc',
        width: 300,
        render(row: ConfigItem) {
            if (row.isEditing) {
                return h(NInput, {
                    value: row.desc,
                    onUpdateValue: (value: string) => {
                        row.desc = value
                    },
                    type: 'textarea',
                    placeholder: '请输入配置详情'
                })
            }
            return row.desc
        }
    },
    {
        title: '说明',
        key: 'shuoming',
        width: 300
    },
    {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
        render(row: ConfigItem) {
            if (row.isEditing) {
                return h('div', {}, [
                    h(
                        NButton,
                        {
                            size: 'small',
                            type: 'primary',
                            onClick: () => {
                                saveConfig(row)
                            },
                            style: { marginRight: '8px' }
                        },
                        { default: () => '确定' }
                    ),
                    h(
                        NButton,
                        {
                            size: 'small',
                            onClick: () => {
                                cancelEdit(row)
                            }
                        },
                        { default: () => '取消' }
                    )
                ])
            }
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            editConfig(row)
                        },
                        style: { marginRight: '8px' }
                    },
                    { default: () => '编辑' }
                )
            ])
        }
    }
])

// 获取配置列表数据
const getConfigList = () => {
    $api.post('/admin/getConfigList').then((res) => {
        if (res.data.code == 200) {
            dataList.length = 0
            dataList.push(...res.data.data.map((item: ConfigItem) => ({
                ...item,
                isEditing: false
            })))
        }
    }).catch((error) => {
        console.error('获取配置列表失败:', error)
        window.$message.error('获取配置列表失败')
    })
}

// 添加新行
const addNewRow = () => {
    // 检查是否已有正在编辑的行
    const editingRow = dataList.find(item => item.isEditing)
    if (editingRow) {
        window.$message.warning('请先完成当前编辑的配置')
        return
    }

    const newConfig: ConfigItem = {
        name: '',
        desc: '',
        isEditing: true
    }
    dataList.unshift(newConfig)
}

// 编辑配置
const editConfig = (row: ConfigItem) => {
    // 检查是否已有正在编辑的行
    const editingRow = dataList.find(item => item.isEditing)
    if (editingRow) {
        window.$message.warning('请先完成当前编辑的配置')
        return
    }

    row.originalName = row.name
    row.originaldesc = row.desc
    row.isEditing = true
}

// 取消编辑
const cancelEdit = (row: ConfigItem) => {
    if (row.id) {
        // 已存在的配置，恢复原值
        row.name = row.originalName || ''
        row.desc = row.originaldesc || ''
        row.isEditing = false
    } else {
        // 新添加的配置，直接删除
        const index = dataList.findIndex(item => item === row)
        if (index > -1) {
            dataList.splice(index, 1)
        }
    }
}

// 保存配置
const saveConfig = (row: ConfigItem) => {
    if (!row.name.trim()) {
        window.$message.error('请输入名称')
        return
    }
    if (!row.desc.trim()) {
        window.$message.error('请输入配置详情')
        return
    }

    const apiUrl = '/admin/saveConfig'
    const requestData = {
        id: row.id,
        title: row.name.trim(),
        desc: row.desc.trim()
    }

    $api.post(apiUrl, requestData).then((res) => {
        if (res.data.code == 200) {
            window.$message.success(row.id ? '修改成功' : '添加成功')
            row.isEditing = false
            if (!row.id) {
                // 新添加的配置，更新ID
                row.id = res.data.data?.id || Date.now()
            }
            // 重新获取列表以确保数据同步
            getConfigList()
        } else {
            window.$message.error(res.data.message || '保存失败')
        }
    }).catch((error) => {
        console.error('保存配置失败:', error)
        window.$message.error('保存配置失败')
    })
}

// 组件挂载时获取数据
onMounted(() => {
    getConfigList()
})
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>
