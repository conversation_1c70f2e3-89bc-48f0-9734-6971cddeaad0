{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vicons/ionicons5": "^0.12.0", "axios": "^1.7.3", "naive-ui": "^2.39.0", "vfonts": "^0.0.3", "vue": "^3.4.31", "vue-router": "^4.4.2"}, "devDependencies": {"@ricons/ionicons5": "^0.12.0", "@vitejs/plugin-vue": "^5.0.5", "typescript": "^5.2.2", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.3", "vite": "^5.3.4", "vue-tsc": "^2.0.24"}}