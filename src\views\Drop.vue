<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="npcId：">
                    <n-input v-model:value="npcId" type="number" clearable placeholder="输入npcId：" />
                </n-form-item>
                <n-form-item label="掉落名称：">
                    <n-input v-model:value="dropName" clearable placeholder="输入掉落名称：" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                掉落物品列表
                <n-button type="primary" @click="showModal = true" style="margin-left: 20px;">添加</n-button>
            </h2>
        </div>
        <div class="contentTable">
            <n-data-table :columns="columns" :data="dataList" />
            <div style="margin: 10px;display: flex;justify-content: center;">
                <n-pagination v-model:page="page" :page-count="pageCount" :on-update:page="getData" />
            </div>
        </div>
    </div>

    <!-- 添加/编辑掉落物品弹窗 -->
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="添加/编辑掉落物品" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="物品名称">
                    <n-input placeholder="请输入物品名称" v-model:value="dropDetail.goodName" />
                </n-form-item>
                <n-form-item label="开始概率">
                    <n-input-number placeholder="请输入开始概率" v-model:value="dropDetail.min" :min="1" />
                </n-form-item>
                <n-form-item label="结束概率">
                    <n-input-number placeholder="请输入最大数量" v-model:value="dropDetail.max" :min="1" />
                </n-form-item>
                <n-form-item label="数量">
                    <n-input-number placeholder="请输入数量" v-model:value="dropDetail.num" :min="1" />
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center">
                    <n-button @click="saveMsg()">保存</n-button>
                    <n-button @click="showModal = false" style="margin-left: 10px;">取消</n-button>
                </div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';


const $api = inject('$api') as AxiosInstance

// 响应式数据
let dropName = ref('')
let npcId = ref<number | null>(null)
let page = ref(1)
let pageCount = ref(1)
let showModal = ref(false)

// 掉落物品接口定义
interface DropItem {
    id: number;
    goodName: string;
    min: number;
    max: number;
    num: number;
}

// 掉落物品详情
let dropDetail: DropItem = reactive({
    id: 0,
    goodName: '',
    min: 1,
    max: 1,
    num: 1,
})

// 数据列表
let dataList: DropItem[] = reactive([])


// 表格列配置
const columns = [
    {
        title: 'ID',
        key: 'id',
        width: 80
    },
    {
        title: '物品名称',
        key: 'goodName',
        width: 150
    },
    {
        title: '开始概率',
        key: 'min',
        width: 100,
    },
    {
        title: '结束概率',
        key: 'max',
        width: 100
    },
    {
        title: '数量',
        key: 'count',
        width: 100
    },
    {
        title: 'npc名称及id',
        key: 'npcName',
        width: 100,
        render(row: any) {
            return row.npc.name+'('+row.npc.id+')'
        }
    },
    {
        title: '操作',
        key: 'actions',
        width: 200,
        render(row: DropItem) {
            return h('div', [
                h(NButton, {
                    size: 'small',
                    type: 'error',
                    style: 'margin-left: 8px',
                    onClick: () => deleteItem(row.id)
                }, '删除')
            ])
        }
    }
]

// 搜索功能
const searchBtn = () => {
    page.value = 1
    getData()
}

// 获取数据
const getData = async (newpage?: number) => {
    newpage && (page.value = newpage)
    let params:any = {size:10,page:page.value}
    if(npcId.value){
        params.id = Number(npcId.value)
    }
    if(dropName.value){
        params.title = dropName.value.trim()
    }
    try {
        $api.post('/admin/getDropGoods', params).then((res) => {
            if(res.data.code==200){
                dataList.length = 0
                dataList.push(...res.data.data)
                pageCount.value = Math.ceil(res.data.total / 10)
            }
        })
    } catch (error) {
        console.error('获取数据失败:', error)
    }
}

// 删除物品
const deleteItem = async (id: number) => {
    try {
        $api.post('/admin/delDropGoods', {id}).then((res) => {
            if(res.data.code==200){
                window.$message.success('删除成功')
                getData()
            }
        })
    } catch (error) {
        console.error('删除失败:', error)
    }
}

// 保存数据
const saveMsg = async () => {
    try {
        if (dropDetail.goodName.trim() === '') {
            alert('请输入物品名称')
            return
        }
        if(!npcId.value){
            alert('请选择npc')
            return
        }

        let params = {
            goodName:dropDetail.goodName,
            min:dropDetail.min,
            max:dropDetail.max,
            num:dropDetail.num,
            npcId:Number(npcId.value)
        }
        $api.post('/admin/addDropGoods', params).then((res) => {
            if(res.data.code==200){
                window.$message.success('操作成功')
                showModal.value = false
                getData()
            }
        })
    } catch (error) {
        console.error('保存失败:', error)
    }
}


// 组件挂载时获取数据
const route = useRoute();
onMounted(() => {
    let id = route.query.id;
    if(id){
        npcId.value=Number(id)
    }
    getData()
})
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>