<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="物品名称：">
                    <n-input v-model:value="searchName" clearable placeholder="输入物品名称搜索" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                兑换物品列表
                <n-button type="primary" @click="openAddModal" style="margin-left: 20px;">添加兑换物品</n-button>
            </h2>
        </div>
        <div class="contentTable">
            <n-data-table 
                :columns="columns" 
                :data="dataList"
                :scroll-x="800" 
            />
            <div style="margin: 10px;display: flex;justify-content: center;">
                <n-pagination v-model:page="page" :page-count="pageCount" :on-update:page="getData" />
            </div>
        </div>
    </div>

    <!-- 添加/编辑兑换物品模态框 -->
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" :title="isEdit ? '编辑兑换物品' : '添加兑换物品'" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="物品名称">
                    <n-input placeholder="请输入物品名称" v-model:value="formData.goodName">
                    </n-input>
                </n-form-item>
                <n-form-item label="需要数量">
                    <n-input-number placeholder="请输入需要数量" v-model:value="formData.num" :min="1">
                    </n-input-number>
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center">
                    <n-button @click="showModal = false" style="margin-right: 10px;">取消</n-button>
                    <n-button type="primary" @click="saveData">保存</n-button>
                </div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';

const $api = inject('$api') as AxiosInstance

// 响应式数据
let searchName = ref('')
let showModal = ref(false)
let isEdit = ref(false)
let page = ref(1)
let pageCount = ref(0)

// 表单数据
let formData = ref({
    id: undefined as number | undefined,
    goodName: '',
    num: 1,
})

// 兑换物品接口
interface GoodsRequirementItem {
    id: number
    goodName: string
    num: number
    remark: string
    createTime?: string
    updateTime?: string
}

// 数据列表
let dataList: GoodsRequirementItem[] = reactive([])

// 表格列定义
let columns = ref([
    {
        title: '物品名称',
        key: 'goodName',
        width: 100
    },
    {
        title: '需要数量',
        key: 'needNum',
        width: 120
    },
    {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
        render(row: GoodsRequirementItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'error',
                        onClick: () => {
                            deleteItem(row.id)
                        }
                    },
                    '删除'
                )
            ])
        }
    }
])

// 搜索功能
const searchBtn = () => {
    page.value = 1
    getData()
}

// 获取数据
const getData = (newpage?: number) => {
    newpage && (page.value = newpage)
    $api.post('/admin/getExchangeList', {
        goodName: searchName.value.trim(),
        page: page.value,
        size: 10
    }).then((res) => {
        if (res.data.code == 200) {
            dataList.length = 0
            dataList.push(...res.data.data)
            pageCount.value = Math.ceil(res.data.total / 10)
        }
    })
}

// 打开添加模态框
const openAddModal = () => {
    isEdit.value = false
    formData.value = {
        id: undefined,
        goodName: '',
        num: 1,
    }
    showModal.value = true
}

// 保存数据
const saveData = () => {
    if (!formData.value.goodName.trim()) {
        window.$message.error('请输入物品名称')
        return
    }
    if (!formData.value.num || formData.value.num < 1) {
        window.$message.error('请输入正确的需要数量')
        return
    }

    const apiUrl = isEdit.value ? '/admin/updateGoodsRequirement' : '/admin/addExchange'
    $api.post(apiUrl, formData.value).then((res) => {
        if (res.data.code == 200) {
            showModal.value = false
            window.$message.success(isEdit.value ? '修改成功' : '添加成功')
            getData()
        } else {
            window.$message.error(res.data.message || '操作失败')
        }
    })
}

// 删除数据
const deleteItem = (id: number) => {
    window.$dialog.warning({
        title: '确认删除',
        content: '确定要删除这个兑换吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
            $api.post('/admin/delExchange', { id }).then((res) => {
                if (res.data.code == 200) {
                    window.$message.success('删除成功')
                    // 如果当前页只有一条数据且不是第一页，则删除后返回上一页
                    if (dataList.length === 1 && page.value > 1) {
                        page.value--
                    }
                    getData()
                } else {
                    window.$message.error(res.data.message || '删除失败')
                }
            })
        }
    })
}

// 组件挂载时获取数据
onMounted(() => {
    getData()
})
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
