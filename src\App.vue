<template>
  <div id="app">
    <n-space vertical>
      <n-layout has-sider>
        <n-layout-sider bordered collapse-mode="width" :collapsed-width="64" :width="240" show-trigger
          >
          <n-menu :collapsed-width="64" :collapsed-icon-size="22" :options="menuOptions" v-model:value="selectedKey"/>
        </n-layout-sider>
        <n-layout>
          <router-view />
        </n-layout>
      </n-layout>
    </n-space>
    <nav>
    </nav>

  </div>
  <n-message-provider>
    <Message/>
  </n-message-provider>
  <n-dialog-provider>
    <Dialog/>
  </n-dialog-provider>
</template>

<script lang="ts" setup>
import type { Component } from 'vue'
import {h, onMounted, provide, ref} from 'vue'
import { NIcon } from 'naive-ui'
import type { MenuOption } from 'naive-ui'
import {
  SettingsSharp,Map,Accessibility,Person,EyedropOutline,
  ListSharp,Book,Egg,Skull
} from '@vicons/ionicons5'
import { RouterLink } from 'vue-router'
import Message from './components/Message.vue'
import Dialog from './components/Dialog.vue'
function renderIcon(icon: Component) {
  return () => h(NIcon, null, { default: () => h(icon) })
}
//利用provider 暴露changeSelectedKey
const selectedKey = ref('map')
const changeSelectedKey = (key: string) => {
  selectedKey.value = key
}
provide('changeSelectedKey', changeSelectedKey)
const menuOptions: MenuOption[] = [
  {
    label: '系统',
    key: 'SettingsSharp',
    icon: renderIcon(SettingsSharp),
    children: [
      {
        label: () =>h(RouterLink,{to: {name: 'Map',}},{ default: () => '地图可视化' }),
        key: 'map',
        icon: renderIcon(Map)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'MapInfo',}},{ default: () => '地图' }),
        key: 'mapInfo',
        icon: renderIcon(Map)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'Npc',}},{ default: () => 'npc' }),
        key: 'npc',
        icon: renderIcon(Accessibility)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'Drop',}},{ default: () => 'drop' }),
        key: 'drop',
        icon: renderIcon(Accessibility)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'General',}},{ default: () => '武将' }),
        key: 'general',
        icon: renderIcon(Person)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'Soldier',}},{ default: () => '士兵' }),
        key: 'soldier',
        icon: renderIcon(Person)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'Skill',}},{ default: () => '技能' }),
        key: 'skill',
        icon: renderIcon(EyedropOutline)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'SkillTemplate',}},{ default: () => '技能模板' }),
        key: 'skillTemplate',
        icon: renderIcon(ListSharp)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'GoodsTemplate',}},{ default: () => '物品列表' }),
        key: 'goodsTemplate',
        icon: renderIcon(Egg)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'GoodsRequirement',}},{ default: () => '兑换物品' }),
        key: 'goodsRequirement',
        icon: renderIcon(ListSharp)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'SkillTemplate',}},{ default: () => '系统任务' }),
        key: 'task',
        icon: renderIcon(Book)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'Moniqi',}},{ default: () => '模拟器' }),
        key: 'moniqi',
        icon: renderIcon(Book)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'ConfigList',}},{ default: () => '配置列表' }),
        key: 'configList',
        icon: renderIcon(SettingsSharp)
      },
      {
        label: () =>h(RouterLink,{to: {name: 'WorldBoss',}},{ default: () => '世界Boss' }),
        key: 'worldBoss',
        icon: renderIcon(Skull)
      },
    ]
  },

]
onMounted(() => {
  let pathname=window.location.pathname
  if(pathname.length>2){
    pathname=pathname.slice(1)
    changeSelectedKey(pathname)
  }
  
})
</script>