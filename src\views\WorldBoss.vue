<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="Boss名称：">
                    <n-input v-model:value="searchName" clearable placeholder="输入Boss名称搜索" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                世界Boss列表
                <n-button type="primary" @click="openAddModal" style="margin-left: 20px;">添加Boss</n-button>
            </h2>
        </div>
        <div class="contentTable">
            <n-data-table 
                :columns="columns" 
                :data="dataList"
                :scroll-x="1000" 
            />
            <div style="margin: 10px;display: flex;justify-content: center;">
                <n-pagination v-model:page="page" :page-count="pageCount" :on-update:page="getData" />
            </div>
        </div>
    </div>

    <!-- 添加/编辑Boss模态框 -->
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" :title="isEdit ? '编辑世界Boss' : '添加世界Boss'" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="Boss名称">
                    <n-input placeholder="请输入Boss名称" v-model:value="formData.name">
                    </n-input>
                </n-form-item>
                <n-form-item label="开始时间">
                    <n-time-picker
                        v-model:formatted-value="formData.startTime"
                        placeholder="请选择开始时间"
                        style="width: 100%"
                        format="HH:mm"
                        value-format="HH:mm"
                    />
                </n-form-item>
                <n-form-item label="结束时间">
                    <n-time-picker
                        v-model:formatted-value="formData.endTime"
                        placeholder="请选择结束时间"
                        style="width: 100%"
                        format="HH:mm"
                        value-format="HH:mm"
                    />
                </n-form-item>
                <n-form-item label="杀死间隔(天)">
                    <n-input-number 
                        placeholder="请输入杀死间隔天数" 
                        v-model:value="formData.intervalDay" 
                        :min="1"
                        style="width: 100%"
                    />
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center">
                    <n-button @click="showModal = false" style="margin-right: 10px;">取消</n-button>
                    <n-button type="primary" @click="saveData">保存</n-button>
                </div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';

const $api = inject('$api') as AxiosInstance

// 响应式数据
let searchName = ref('')
let showModal = ref(false)
let isEdit = ref(false)
let page = ref(1)
let pageCount = ref(0)

// 表单数据
let formData = ref({
    id: undefined as number | undefined,
    name: '',
    startTime: '' as string,
    endTime: '' as string,
    intervalDay: 1
})

// 世界Boss接口
interface WorldBossItem {
    id: number
    name: string
    startTime: string
    endTime: string
    intervalDay: number
    createTime?: string
    updateTime?: string
}

// 数据列表
let dataList: WorldBossItem[] = reactive([])

// 表格列定义
let columns = ref([
    {
        title: 'ID',
        key: 'id',
        width: 80
    },
    {
        title: 'Boss名称',
        key: 'name',
        width: 200
    },
    {
        title: '开始时间',
        key: 'startTime',
        width: 120
    },
    {
        title: '结束时间',
        key: 'endTime',
        width: 120
    },
    {
        title: '杀死间隔(天)',
        key: 'intervalDay',
        width: 120
    },
    {
        title: '创建时间',
        key: 'createTime',
        width: 180,
        render(row: WorldBossItem) {
            return row.createTime ? new Date(row.createTime).toLocaleString() : '-'
        }
    },
    {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
        render(row: WorldBossItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            openEditModal(row)
                        },
                        style: { marginRight: '8px' }
                    },
                    { default: () => '编辑' }
                )
            ])
        }
    }
])

// 搜索功能
const searchBtn = () => {
    page.value = 1
    getData()
}

// 获取数据
const getData = (newpage?: number) => {
    newpage && (page.value = newpage)
    $api.post('/admin/getBossList', {
        name: searchName.value.trim(),
        page: page.value,
        size: 10
    }).then((res) => {
        if (res.data.code == 200) {
            dataList.length = 0
            dataList.push(...res.data.data)
            pageCount.value = Math.ceil(res.data.total / 10)
        }
    }).catch((error) => {
        console.error('获取Boss列表失败:', error)
        window.$message.error('获取Boss列表失败')
    })
}

// 打开添加模态框
const openAddModal = () => {
    isEdit.value = false
    formData.value = {
        id: undefined,
        name: '',
        startTime: '',
        endTime: '',
        intervalDay: 1
    }
    showModal.value = true
}

// 打开编辑模态框
const openEditModal = (row: WorldBossItem) => {
    isEdit.value = true
    formData.value = {
        id: row.id,
        name: row.name,
        startTime: row.startTime || '',
        endTime: row.endTime || '',
        intervalDay: row.intervalDay
    }
    showModal.value = true
}

// 保存数据
const saveData = () => {
    if (!formData.value.name.trim()) {
        window.$message.error('请输入Boss名称')
        return
    }
    if (!formData.value.startTime) {
        window.$message.error('请选择开始时间')
        return
    }
    if (!formData.value.endTime) {
        window.$message.error('请选择结束时间')
        return
    }
    // 比较时间字符串
    if (formData.value.startTime >= formData.value.endTime) {
        window.$message.error('结束时间必须大于开始时间')
        return
    }
    if (!formData.value.intervalDay || formData.value.intervalDay < 1) {
        window.$message.error('请输入正确的杀死间隔天数')
        return
    }

    const requestData = {
        id: formData.value.id,
        name: formData.value.name.trim(),
        startTime: formData.value.startTime,
        endTime: formData.value.endTime,
        intervalDay: formData.value.intervalDay
    }

    $api.post('/admin/addBoss', requestData).then((res) => {
        if (res.data.code == 200) {
            showModal.value = false
            window.$message.success(isEdit.value ? '修改成功' : '添加成功')
            getData()
        } else {
            window.$message.error(res.data.message || '操作失败')
        }
    }).catch((error) => {
        console.error('保存Boss失败:', error)
        window.$message.error('保存Boss失败')
    })
}

// 删除数据
// const deleteItem = (id: number) => {
//     window.$dialog.warning({
//         title: '确认删除',
//         content: '确定要删除这个世界Boss吗？',
//         positiveText: '确定',
//         negativeText: '取消',
//         onPositiveClick: () => {
//             $api.post('/admin/deleteBoss', { id }).then((res) => {
//                 if (res.data.code == 200) {
//                     window.$message.success('删除成功')
//                     // 如果当前页只有一条数据且不是第一页，则删除后返回上一页
//                     if (dataList.length === 1 && page.value > 1) {
//                         page.value--
//                     }
//                     getData()
//                 } else {
//                     window.$message.error(res.data.message || '删除失败')
//                 }
//             }).catch((error) => {
//                 console.error('删除Boss失败:', error)
//                 window.$message.error('删除Boss失败')
//             })
//         }
//     })
// }

// 组件挂载时获取数据
onMounted(() => {
    getData()
})
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
