<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="地图id：">
                    <n-input-number v-model:value="mapId" clearable placeholder="输入地图id：" :allow-input="onlyAllowNumber"/>
                </n-form-item>
                <n-form-item label="地图名称：">
                    <n-input v-model:value="mapName" clearable placeholder="输入地图名称：" :allow-input="noSideSpace" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                地图列表
                <n-button attr-type="button" @click="clearCache">
                        清理缓存
                    </n-button>
            </h2>
            
        </div>
        <div class="contentTable">
            <n-data-table 
                :columns="columns" 
                :data="dataList" 
                :scroll-x="1800"
            />
            <div style="margin: 10px;display: flex;justify-content: center;">
                <n-pagination v-model:page="page" :page-count="pageCount" :on-update:page="getData" />
            </div>
        </div>
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="添加/编辑地图" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="地图名称">
                    <n-input placeholder="请输入地图名称" v-model:value="npcDetail.title">
                    </n-input>
                </n-form-item>
                <n-form-item label="地图描述">
                    <n-input placeholder="请输入地图描述" v-model:value="npcDetail.desc">
                    </n-input>
                </n-form-item>
                <n-form-item label="上Id">
                    <n-input type="number" placeholder="请输入上Id" v-model:value="npcDetail.topId">
                    </n-input>
                </n-form-item>
                <n-form-item label="下Id">
                    <n-input type="number" placeholder="请输入下Id" v-model:value="npcDetail.bottomId">
                    </n-input>
                </n-form-item>
                <n-form-item label="左Id">
                    <n-input type="number" placeholder="请输入左Id" v-model:value="npcDetail.leftId">
                    </n-input>
                </n-form-item>
                <n-form-item label="右Id">
                    <n-input type="number" placeholder="请输入右Id" v-model:value="npcDetail.rightId">
                    </n-input>
                </n-form-item>
                <n-form-item label="副本名称">
                    <n-input placeholder="如果当前地图是副本中，此选项必填否则会报错" v-model:value="npcDetail.dungeonName">
                    </n-input>
                </n-form-item>
                <n-form-item label="进入条件类型">
                    <n-select v-model:value="npcDetail.conditionType" :options="conditionTypeOptions" />
                </n-form-item>
                <n-form-item label="来源地图ID" v-if="npcDetail.conditionType !== 0">
                    <n-input-number placeholder="当有进入条件类型时必填" v-model:value="npcDetail.sourceGpsId" :min="1">
                    </n-input-number>
                </n-form-item>
                <n-form-item label="要清理的地图名称">
                    <n-input placeholder="当条件是打怪时必填，多个地图用|隔开" v-model:value="npcDetail.mapName">
                    </n-input>
                </n-form-item>
                <n-form-item label="所需要的物品">
                    <n-input placeholder="当条件是需要物品时必填，物品1|数量&物品2|数量" v-model:value="npcDetail.goodName">
                    </n-input>
                </n-form-item>
                <n-form-item label="最大进入次数">
                    <n-input-number placeholder="当条件是进入副本时必填，请输入每日进入次数" v-model:value="npcDetail.maxCount">
                    </n-input-number>
                </n-form-item>
                <n-form-item label="提示信息">
                    <n-input placeholder="条件不满足的提示信息" v-model:value="npcDetail.msg">
                    </n-input>
                </n-form-item>
                <n-form-item label="显示传送地图">
                    <n-input placeholder="页面中间的内容，比如柳树林的进入树洞，进入树洞|6968&地图2|ID" v-model:value="npcDetail.showMapName">
                    </n-input>
                </n-form-item>
                <n-form-item label="跳转其他页面">
                    <n-input placeholder="比如爬大柱" v-model:value="npcDetail.otherMap">
                    </n-input>
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="saveMsg()">保存</n-button></div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const $api = inject('$api') as AxiosInstance
let mapId = ref<number>()
let mapName = ref('')
let showModal = ref(false)
let page = ref(1)
let pageCount = ref(0)
let npcDetail = ref<MapItem>({
    id: null,
    title: null,
    desc: null,
    topId: null,
    bottomId: null,
    leftId: null,
    rightId: null,
    top: null,
    bottom: null,
    left: null,
    right: null,
    type: null,
    // New fields
    conditionType: 0,
    mapName: null,
    goodName: null,
    maxCount: null,
    msg: null,
    dungeonName: null,
    showMapName: null,
    sourceGpsId: null,
    otherMap:null
})
interface MapItem {
    id: number|null;
    title: string|null;
    desc: string|null;
    topId: number|null;
    bottomId: number|null;
    leftId: number|null;
    rightId: number|null;
    top: string|null;
    bottom: string|null;
    left: string|null;
    right: string|null;
    type: number|null;
    // New fields
    conditionType: number;
    mapName: string|null;
    goodName: string|null;
    maxCount: number|null;
    msg: string|null;
    dungeonName: string|null;
    showMapName: string|null;
    sourceGpsId: number|null;
    otherMap: string|null;

}
let dataList: MapItem[] = reactive([
])
const onlyAllowNumber = (value: string) => {
    return /^[0-9]*$/.test(value)
}
const noSideSpace = (value: string) => {
    return value.trim() === value
}
let columns = ref([
    {
        title: '地图名称',
        key: 'title',
        width: 150,
        fixed: 'left'
    },
    {
        title: '地图id',
        key: 'id',
        width: 100,
        fixed: 'left'
    },
    {
        title: '地图描述',
        key: 'desc',
    },
    {
        title: '上',
        key: 'top',
    },
    {
        title: '上Id',
        key: 'topId',
    },
    {
        title: '下',
        key: 'bottom',
    },
    {
        title: '下Id',
        key: 'bottomId',
    },
    {
        title: '左',
        key: 'left',
    },
    {
        title: '左Id',
        key: 'leftId',
    },
    {
        title: '右',
        key: 'right',
    },
    {
        title: '右Id',
        key: 'rightId',

    },
    {
        title: '副本名称',
        key: 'dungeonName',
    },
    {
        title: '进入条件类型',
        key: 'conditionType',
        render(row: MapItem) {
            const types = {
                0: '无',
                1: '打怪',
                2: '需要物品',
                3: '副本'
            };
            return types[row.conditionType as keyof typeof types] || '无条件';
        }
    },
    {
        title: '来源地图ID',
        key: 'sourceGpsId',
        width: 120
    },
    {
        title: '要清理的地图',
        key: 'mapName',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '所需物品',
        key: 'goodName',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '最大进入次数',
        key: 'maxCount'
    },
    {
        title: '显示传送地图',
        key: 'showMapName',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '其他配置',
        key: 'otherMap',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '操作',
        key: 'desc',
        fixed: 'right',
        width: 100,
        render(row: MapItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            showModal.value = true
                            npcDetail.value = row
                        }, 
                    },
                    { default: () => '编辑' }
                )
            ])
        },
    },
])
const searchBtn = () => {
    page.value=1
    getData()
}
const getData = (newpage?: number) => {
    newpage && (page.value = newpage)
    $api.post('/admin/getList', { id:mapId.value,page:page.value,size:10,title:mapName.value }).then((res) => {
        dataList.length = 0
        dataList.push(...res.data.data)
        pageCount.value = Math.ceil(res.data.total / 10)
    })
}
const saveMsg = () => {
    // 验证当有进入条件类型时，sourceGpsId 必须填写
    if (npcDetail.value.conditionType !== 0 && !npcDetail.value.sourceGpsId) {
        window.$message.error('当有进入条件类型时，来源地图ID为必填项')
        return
    }

    npcDetail.value.leftId = Number(npcDetail.value.leftId)||null
    npcDetail.value.rightId = Number(npcDetail.value.rightId)||null
    npcDetail.value.topId = Number(npcDetail.value.topId)||null
    npcDetail.value.bottomId = Number(npcDetail.value.bottomId)||null
    $api.post('/admin/updateMap', {...npcDetail.value}).then((res) => {
        if(res.data.code==200){
            showModal.value = false
            window.$message.success('操作成功')
            getData()
        }else{
            window.$message.error('操作失败')
        }
    })
}
const clearCache = () => {
    $api.get('/admin/clearRedisCache').then((res) => {
        if(res.data.code==200){
            window.$message.success('清理缓存成功')
        }else{
            window.$message.error('清理缓存失败')
        }
    })
}
const route = useRoute();
onMounted(() => {
    let id = route.query.mapId;
    if(id){
        mapId.value=Number(id)
    }
    getData()
});
let conditionTypeOptions = [
    { label: '无条件', value: 0 },
    { label: '打怪', value: 1 },
    { label: '需要物品', value: 2 },
    { label: '进入副本', value: 3 }
]
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>