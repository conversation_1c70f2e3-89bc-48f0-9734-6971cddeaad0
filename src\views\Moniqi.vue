<template>
    <h1 style="text-align: center;">帝王百级练兵数据模拟器（QQ群：278353668）</h1>
    <div class="container">
        <n-card title="角色">
            <n-form label-placement="left" :label-width="80">
                <n-form-item label="角色名称:">
                    <n-input v-model:value="role.rolename" placeholder="请输入角色名称" />
                </n-form-item>
                <n-form-item label="等级:">
                    <n-input-number v-model:value="role.rolelvl" placeholder="请输入等级" :min="1" :max="308"
                        :update-value-on-input="false" clearable />
                </n-form-item>
                <n-form-item label="魄力:">
                    <n-input-number v-model:value="role.rolepoli" placeholder="请输入魄力" :min="0" :max="role.rolelvl + 15" clearable
                        :update-value-on-input="false" />
                </n-form-item>
        
            </n-form>
        </n-card>
        <n-card title="武将">
            <n-form label-placement="left" :label-width="80">
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="等级:">
                            <n-input-number v-model:value="role.generallvl" placeholder="请输入等级" :min="1" :max="role.lvl"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="魄力:">
                            <n-input-number v-model:value="role.generalpoli" placeholder="请输入魄力" :min="0"
                                :max="role.generallvl + 15" clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-form-item label="装备攻击力:">
                    <n-input-number  disabled :value="role.generalzhuangbeigongji" />
                </n-form-item>
                <n-form-item label="装备防御力:">
                    <n-input-number  disabled :value="role.generalzhuangbeifangyu" />
                </n-form-item>
                <n-form-item label="装备血量:">
                    <n-input-number  disabled :value="role.generalzhuangbeixueliang" />
                </n-form-item>
                <n-form-item label="攻击:">
                    <n-input-number  disabled :value="role.generalgongji" />
                </n-form-item>
                <n-form-item label="防御:">
                    <n-input-number  disabled :value="role.generalfangyu" />
                </n-form-item>
                <n-form-item label="血量:">
                    <n-input-number disabled v-model:value="role.generalxueliang"/>
                </n-form-item>
            </n-form>
        </n-card>
        <n-card title="兵种">
            <n-form label-placement="left" :label-width="80">
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="兵种:">
                            <n-input v-model:value="role.soldiersname" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="等级:">
                            <n-input-number v-model:value="role.soldierslvl" placeholder="请输入等级" :min="1" :max="role.generallvl"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-form-item label="装备攻击力:">
                    <n-input-number  disabled :value="role.soldierszhuangbeigongji" />
                </n-form-item>
                <n-form-item label="装备防御力:">
                    <n-input-number  disabled :value="role.soldierszhuangbeifangyu" />
                </n-form-item>
                <n-form-item label="装备血量:">
                    <n-input-number  disabled :value="role.soldierszhuangbeixueliang" />
                </n-form-item>
                
                <n-form-item label="攻击:">
                    <n-input-number disabled v-model:value="role.soldiersgongji" />
                </n-form-item>
                <n-form-item label="防御:">
                    <n-input-number disabled v-model:value="role.soldiersfangyu" />
                </n-form-item>
                <n-form-item label="血量:">
                    <n-input-number disabled v-model:value="role.soldiersxueliang"/>
                </n-form-item>
            </n-form>
        </n-card>
    </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
const role = ref({
    rolename: '测试角色',
    rolelvl: 100,
    rolepoli: 100,
    roleguanzhi: 0,
    rolerenkou: 0,
    generalname: '测试武将',
    generallvl: 1000,
    generalpoli: 100,
    generalgongji  : 0,
    generalfangyu: 0,
    generalxueliang: 0,
    generalzhuangbeigongji: 1000,
    generalzhuangbeifangyu: 10000,
    generalzhuangbeixueliang: 1000,
    soldiersname: '测试兵种',
    soldierslvl: 0,
    soldiersgongji: 0,
    soldiersfangyu: 0,
    soldiersxueliang: 0,
    soldierszhuangbeigongji: 0,
    soldierszhuangbeifangyu: 0,
    soldierszhuangbeixueliang: 0,
})
watch(
    role,
    (newVal, oldVal) => {
        // 这里可以处理role任意属性变化后的逻辑
        console.log('role变化了', newVal, oldVal)
        role.value.generalgongji = newVal.rolepoli + newVal.generalpoli
        role.value.soldiersxueliang = newVal.rolepoli + newVal.soldierspoli
    },
    { deep: true }
)
</script>

<style scoped>
.container {
    display: flex;
    justify-content: space-around;
}

.n-card {
    max-width: 30%;
}
</style>
