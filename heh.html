<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script>
        const getPopupFieldConfig = (item) => {
            const { formSchema } = item
            const { destFields = '', orgFields = '' } = formSchema
            
            const result = orgFields.split(',').map((oField, index) => {
                return {
                    source: oField,
                    target: destFields.split(',')[index],
                }
            })
            return result
        }
        getPopupFieldConfig({
            "dbPointLength": 0,
            "mode": "single",
            "popupMulti": true,
            "view": "popup",
            "code": "baoxian_list",
            "orgFields": "id,client_name",
            "type": "string",
            "title": "交险投保公司",
            "order": 13,
            "destFields": "force_insurance_com_id,force_insurance_com"
        })
    </script>
</body>

</html>