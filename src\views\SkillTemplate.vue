<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="技能名称：">
                    <n-input v-model:value="skillName" clearable placeholder="输入技能名称" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                技能列表
                <n-button type="primary" @click="showModal=true" style="margin-left: 20px;">添加</n-button>
            </h2>
            
        </div>
        <div class="contentTable">
            <n-data-table :columns="columns" :data="dataList" />
        </div>
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="添加/编辑技能" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="技能名称">
                    <n-input placeholder="请输入技能名称" v-model:value="skillDetail.name">
                    </n-input>
                </n-form-item>
                <n-form-item label="伤害系数">
                    <n-input-number placeholder="请输入伤害系数" v-model:value="skillDetail.damage"/>
                </n-form-item>
                <n-form-item label="伤害距离">
                    <n-input-number placeholder="请输入伤害距离" v-model:value="skillDetail.distance"/>
                </n-form-item>
                <n-form-item label="伤害数量">
                    <n-input-number placeholder="请输入伤害数量" v-model:value="skillDetail.damageNum"/>
                </n-form-item>
                <n-form-item label="武器类型">
                    <n-select v-model:value="skillDetail.weaponType" :options="weaponOptions" />
                </n-form-item>
                <n-form-item label="冷却时间">
                    <n-input-number placeholder="请输入冷却时间" v-model:value="skillDetail.interval"/>
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="saveMsg()">保存</n-button></div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';

const $api = inject('$api') as AxiosInstance
let skillName = ref('')
interface NpcItem {
    id: number
    name: string
    damage: number
    distance: number
    damageNum: number
    weaponType: number
    interval: number
}
let weaponOptions = [
    {label: '任意',value: 0,},
    {label: '刀',value: 21,},
    {label: '剑',value: 22,},
    {label: '棍',value: 23,},
    {label: '斧',value: 24,},
    {label: '锤',value: 25,},
    {label: '枪',value: 26,},
]
let showModal = ref(false)
let dataList: NpcItem[] = reactive([
    // { id: 1, name: 'npc', desc: 'npc的描述', directAttack: 0, canAttack: 0, canBuy: 0, gpsName: 1, intervalTime: 0 },
])
let skillDetail= ref({
    id: undefined as number|undefined,
    name: '',
    damage: undefined as number|undefined,
    distance: undefined as number|undefined,
    damageNum: undefined as number|undefined,
    weaponType: 0,
    interval: 0,
})
let columns = ref([
    {
        title: 'id',
        key: 'id'
    },
    {
        title: '名称',
        key: 'name'
    },
    {
        title: '伤害系数',
        key: 'damage',
    },
    {
        title: '伤害距离',
        key: 'distance'
    },
    {
        title: '伤害数量',
        key: 'damageNum'
    },
    {
        title: '武器类型',
        key: 'weaponType',
        render(row: NpcItem) {
            let obj = {
                21: '刀',
                22: '剑',
                23: '棍',
                24: '斧',
                25: '锤',
                26: '枪',
                '0': '任意'
            };
            return obj[row.weaponType as keyof typeof obj]
        }
    },
    {
        title: '间隔时间',
        key: 'interval'
    },
    {
        title: '操作',
        key: 'desc',
        fixed: 'right',
        width: 300,
        render(row: NpcItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            showModal.value = true
                            skillDetail.value = row
                        },
                    },
                    { default: () => '编辑' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            delSkill(row.id)
                        },
                    },
                    { default: () => '删除' }
                )
            ])
        },
    },
])
const searchBtn = () => {
    getData()
}
const getData = () => {
    $api.post('/admin/getSkillTemplate', {title: skillName.value}).then((res) => {
        if(res.data.code==200){
            dataList.length = 0
            dataList.push(...res.data.data)
            skillDetail.value = {
                id: undefined as number|undefined,
                name: '',
                damage: undefined as number|undefined,
                distance: undefined as number|undefined,
                damageNum: undefined as number|undefined,
                weaponType: 0,
                interval: 0,
            }
        }
    })
}
const saveMsg = () => {
    $api.post('/admin/addSkillTemplate', skillDetail.value).then((res) => {
        if(res.data.code==200){
            showModal.value = false
            window.$message.success('操作成功')
            getData()
        }else{

        }
    })
}
const delSkill = (id: number) => {
    $api.post('/admin/delSkillTemplate', {id}).then((res) => {
        if(res.data.code==200){
            window.$message.success('删除成功')
            getData()
        }
    })
}
onMounted(() => {
    getData()
});
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>