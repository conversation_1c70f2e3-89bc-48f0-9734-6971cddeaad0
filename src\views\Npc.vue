<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="地图id：">
                    <n-input-number v-model:value="mapId" clearable placeholder="输入地图id：" />
                </n-form-item>
                <n-form-item label="npc名称：">
                    <n-input v-model:value="npcName" clearable placeholder="输入npc名称：" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                npc列表
                <n-flex>
                    <n-button type="primary" @click="showModal=true" style="margin-left: 20px;">添加</n-button>
                    <n-input-number v-model:value="npcId" clearable placeholder="输入npcid：" />
                    <n-button type="primary" @click="copyNpc" style="margin-left: 20px;">复制npc</n-button>
                </n-flex>
                
            </h2>
            
        </div>
        <div class="contentTable">
            <n-data-table 
                :columns="columns" 
                :data="dataList"
                :scroll-x="1800" 
            />
            <div style="margin: 10px;display: flex;justify-content: center;">
                <n-pagination v-model:page="page" :page-count="pageCount" :on-update:page="getData" />
            </div>
        </div>
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="添加/编辑npc" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="npc名称">
                    <n-input placeholder="请输入npc名称" v-model:value="npcDetail.name">
                    </n-input>
                </n-form-item>
                <n-form-item label="npc描述">
                    <n-input placeholder="请输入npc描述" v-model:value="npcDetail.desc">
                    </n-input>
                </n-form-item>
                <n-form-item label="是否直接攻击">
                    <n-select v-model:value="npcDetail.directAttack" :options="[{label:'否',value:0},{label:'是',value:1}]" />
                </n-form-item>
                <n-form-item label="可攻击">
                    <n-select v-model:value="npcDetail.canAttack" :options="[{label:'否',value:0},{label:'是',value:1}]" />
                </n-form-item>
                <n-form-item label="可购买">
                    <n-select v-model:value="npcDetail.canBuy" :options="[{label:'否',value:0},{label:'是',value:1}]" />
                </n-form-item>
                <n-form-item label="间隔时间">
                    <n-input-number placeholder="请输入冷却时间" v-model:value="npcDetail.intervalTime"/>
                </n-form-item>
                <n-form-item label="潜能">
                    <n-input-number placeholder="请输入潜能" v-model:value="npcDetail.potential"/>
                </n-form-item>
                <n-form-item label="银两">
                    <n-input-number placeholder="请输入银两" v-model:value="npcDetail.gold"/>
                </n-form-item>
                <n-form-item label="粮草">
                    <n-input-number placeholder="请输入粮草" v-model:value="npcDetail.food"/>
                </n-form-item>
                <n-form-item label="木材">
                    <n-input-number placeholder="请输入木材" v-model:value="npcDetail.wood"/>
                </n-form-item>
                <n-form-item label="石料">
                    <n-input-number placeholder="请输入石料" v-model:value="npcDetail.stone"/>
                </n-form-item>
                <n-form-item label="生铁">
                    <n-input-number placeholder="请输入生铁" v-model:value="npcDetail.iron"/>
                </n-form-item>
                <n-form-item label="显示间隔时间">
                    <n-select v-model:value="npcDetail.showIntervalTime" :options="[{label:'否',value:1},{label:'是',value:2}]" />
                </n-form-item>
                <n-form-item label="副本名称">
                    <n-input placeholder="副本最后一个怪才写,打完会退出副本" v-model:value="npcDetail.dungeonName"/>
                </n-form-item>
                <n-form-item label="副本声望">
                    <n-input-number placeholder="副本最后一个怪才写" v-model:value="npcDetail.reputation"/>
                </n-form-item>
                <n-form-item label="设置">
                    <n-input placeholder="设置，请看下方文字" v-model:value="npcDetail.config"/>
                </n-form-item>
                <p style="color: pink;">
                打开npc显示的额外选项比如 进入副本，兑换/领取物品，进入另外的页面,1是进入副本,2是兑换/领取物品,3是进入另外的页面，例子：进入黑风岭副本|1&研制强体奇书|2
                </p>
                <p style="color: pink;">
                例子：进入黑风岭副本|1&研制强体奇书|2
                </p>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="saveMsg()">保存</n-button></div>
            </template>
        </n-card>
    </n-modal>

    <!-- 物品列表模态框 -->
    <n-modal v-model:show="showGoodsModal">
        <n-card style="width: 800px" title="NPC物品列表" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <template #header-extra>
                <n-button type="primary" @click="openAddGoodsModal">添加物品</n-button>
            </template>
            <div>
                <n-data-table
                    :columns="npcGoodsColumns"
                    :data="npcGoodsList"
                    :scroll-x="600"
                />
            </div>
        </n-card>
    </n-modal>

    <!-- 添加物品模态框 -->
    <n-modal v-model:show="showAddGoodsModal">
        <n-card style="width: 900px" title="选择物品" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form inline label-width="auto" label-placement="left" style="margin-bottom: 20px;">
                    <n-form-item label="物品名称：">
                        <n-input v-model:value="goodsSearchName" clearable placeholder="输入物品名称搜索" />
                    </n-form-item>
                    <n-form-item>
                        <n-button attr-type="button" @click="searchGoods">
                            搜索
                        </n-button>
                    </n-form-item>
                </n-form>
                <n-data-table
                    :columns="goodsColumns"
                    :data="goodsList"
                    :scroll-x="1200"
                />
                <div style="margin: 10px;display: flex;justify-content: center;">
                    <n-pagination v-model:page="goodsPage" :page-count="goodsPageCount" :on-update:page="getGoodsList" />
                </div>
            </div>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import router from '../router';
const changeSelectedKey=inject('changeSelectedKey') as Function
const $api = inject('$api') as AxiosInstance
let mapId = ref(5050)
let npcId = ref<number>()
let npcName = ref('')
let showModal = ref(false)
let showGoodsModal = ref(false)
let showAddGoodsModal = ref(false)
let page = ref(1)
let pageCount = ref(0)
let goodsPage = ref(1)
let goodsPageCount = ref(0)
let currentNpcId = ref<number>(0)
let goodsSearchName = ref('')
let npcDetail = ref({
    id: undefined as number|undefined,
    name: '',
    desc: '',
    directAttack: 0,
    canAttack: 0,
    canBuy: 0,
    gpsId: 0,
    intervalTime: 0,
    config:'',
    potential: 0,
    gold: 0,
    food: 0,
    wood: 0,
    stone: 0,
    iron: 0,
    showIntervalTime: 1,
    dungeonName:'',
    reputation:0,
})
interface NpcItem {
    id: number
    name: string
    desc: string
    directAttack: number
    canAttack: number
    canBuy: number
    gpsId: number
    intervalTime: number
    config: string
    potential: number
    gold: number
    food: number
    wood: number
    stone: number
    iron: number
    showIntervalTime: number
    dungeonName:string
    reputation:number
}

interface GoodsItem {
    id: number
    name: string
    desc: string
    price: number
    type: number
    subType: number
    addhp: number
    addatk: number
    adddef: number
    bind: 1 | 2
    weight: number
    useLevel: number
    roleUse: 0 | 1
    generalUse: 0 | 1
    soldierUse: 0 | 1
    fightUse: 0 | 1
    isStack: 1 | 2
    isForge: 0 | 1
    successRate: number
    forgeMaterialCount: number
    forgeMaterialCount100: number
    holeCount: number
}

interface NpcGoodsItem {
    id: number
    npcId: number
    goodId: number
    goodName: string
    price: number
}
let dataList: NpcItem[] = reactive([
    // { id: 1, name: 'npc', desc: 'npc的描述', directAttack: 0, canAttack: 0, canBuy: 0, gpsName: 1, intervalTime: 0 },
])

let goodsList: GoodsItem[] = reactive([])
let npcGoodsList: NpcGoodsItem[] = reactive([])

let addGoodsDetail = ref({
    goodId: 0,
    goodName: '',
    price: 0
})
let columns = ref([
    {
        title: 'id',
        key: 'id'
    },
    {
        title: '名称',
        key: 'name'
    },
    {
        title: '描述',
        key: 'desc',
        width: 200,
        ellipsis: true
    },
    {
        title: '直接攻击',
        key: 'directAttack',
        render(row: NpcItem) {
            return h('span', {}, row.directAttack ? '是' : '否')
        }
    },
    {
        title: '可攻击',
        key: 'canAttack',
        render(row: NpcItem) {
            return h('span', {}, row.canAttack ? '是' : '否')
        }
    },
    {
        title: '可购买',
        key: 'canBuy',
        render(row: NpcItem) {
            return h('span', {}, row.canBuy ? '是' : '否')
        }
    },
    {
        title: '地图id',
        key: 'gpsId'
    },
    {
        title: '杀死间隔',
        key: 'intervalTime'
    },
    {
        title: '设置',
        key: 'config'
    },
    {
        title: '潜能',
        key: 'potential'
    },
    {
        title: '银两',
        key: 'gold'
    },
    {
        title: '粮草',
        key: 'food'
    },
    {
        title: '木材',
        key: 'wood'
    },
    {
        title: '石料',
        key: 'stone'
    },
    {
        title: '生铁',
        key: 'iron'
    },
    {
        title: '副本名称',
        key: 'dungeonName'
    },
    {
        title: '声望',
        key: 'reputation'
    },
    {
        title: '显示间隔时间',
        key: 'showIntervalTime',
        render(row: NpcItem) {
            return h('span', {}, row.showIntervalTime === 2 ? '显示' : '不显示')
        }
    },
    {
        title: '操作',
        key: 'desc',
        fixed: 'right',
        width: 200,
        render(row: NpcItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            toGenerayList(row.id)
                        },
                    },
                    { default: () => '查看武将' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            showModal.value = true
                            npcDetail.value = row
                        }, 
                    },
                    { default: () => '编辑' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            delSkill(row.id) 
                        },
                    },
                    { default: () => '删除' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            openGoodsModal(row.id)
                        },
                    },
                    { default: () => '物品列表' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            toDropList(row.id)
                        },
                    },
                    { default: () => '查看掉落' }
                ),
            ])
        },
    },
])

// NPC物品列表表格列定义
let npcGoodsColumns = ref([
    {
        title: '物品名称',
        key: 'name',
        width: 200
    },
    {
        title: '价格',
        key: 'price',
        width: 100
    },
    {
        title: '操作',
        key: 'action',
        width: 100,
        render(row: NpcGoodsItem) {
            return h(
                NButton,
                {
                    size: 'small',
                    type: 'error',
                    onClick: () => {
                        deleteNpcGoods(row.id)
                    },
                },
                { default: () => '删除' }
            )
        }
    }
])

// 物品列表表格列定义
let goodsColumns = ref([
    {
        title: '物品名称',
        key: 'name',
        width: 50
    },
    {
        title: '价格',
        key: 'price',
        width: 30
    },
    {
        title: '操作',
        key: 'action',
        width: 100,
        render(row: GoodsItem) {
            return h(
                NButton,
                {
                    size: 'small',
                    type: 'primary',
                    onClick: () => {
                        addGoodsToNpc(row)
                    },
                },
                { default: () => '添加' }
            )
        }
    }
])

const searchBtn = () => {
    page.value = 1
    getData()
}
const copyNpc = () => {
    if(!npcId.value){
        window.$message.error('请输入npcid')
        return
    }
    $api.post('/admin/copyNpc', { npcId:npcId.value, gpsId:mapId.value }).then((res) => {
        if(res.data.code==200){
            window.$message.success('复制成功')
            page.value = 1
            getData()
        }else{
            window.$message.error('复制失败')
        }
    })
}
const getData = (newpage?: number) => {
    newpage && (page.value = newpage)
    $api.post('/admin/getNpc', { 
        id: mapId.value,
        title: npcName.value.trim(),
        page: page.value,
        size: 10
    }).then((res) => {
        dataList.length = 0
        dataList.push(...res.data.data)
        pageCount.value = Math.ceil(res.data.total / 10)
        
        npcDetail.value = {
            name: '',
            desc: '',
            id: undefined as number|undefined,
            directAttack: 0,
            canAttack: 0,
            canBuy: 0,
            gpsId: 0,
            config:'',
            intervalTime: 0,
            potential: 0,
            gold: 0,
            food: 0,
            wood: 0,
            stone: 0,
            iron: 0 ,
            showIntervalTime: 1,
            dungeonName: '',
            reputation:0
        }
        npcDetail.value.gpsId = mapId.value
    })
}
//跳转武将页面
const toGenerayList = (id: number) => {
    changeSelectedKey('general')
    router.push({ path: '/general', query: { id:id } })
}
//跳转掉落页面
const toDropList = (id: number) => {
    changeSelectedKey('drop')
    router.push({ path: '/drop', query: { id:id } })
}
const saveMsg = () => {
    $api.post('/admin/addNpc', {...npcDetail.value}).then((res) => {
        if(res.data.code==200){
            showModal.value = false
            window.$message.success('操作成功')
            getData()
        }else{

        }
    })
}
const delSkill = (id: number) => {
    $api.post('/admin/delNpc', {id}).then((res) => {
        if(res.data.code==200){
            window.$message.success('删除成功')
            // 如果当前页只有一条数据且不是第一页，则删除后返回上一页
            if(dataList.length === 1 && page.value > 1) {
                page.value--
            }
            getData()
        }else{
            window.$message.error('npc下没有武将才能删除奥')
        }
    })
}

// 打开物品列表模态框
const openGoodsModal = (npcId: number) => {
    currentNpcId.value = npcId
    showGoodsModal.value = true
    getNpcGoods(npcId)
}

// 获取NPC的物品列表
const getNpcGoods = (npcId: number) => {
    $api.post('/admin/getNpcGoodsList', { npcId }).then((res) => {
        if(res.data.code == 200) {
            npcGoodsList.length = 0
            npcGoodsList.push(...res.data.data)
        }else{
            window.$message.error(res.data.msg)
        }
    })
}

// 打开添加物品模态框
const openAddGoodsModal = () => {
    showAddGoodsModal.value = true
    goodsPage.value = 1
    getGoodsList()
}

// 获取所有物品列表
const getGoodsList = (newpage?: number) => {
    newpage && (goodsPage.value = newpage)
    $api.post('/admin/getGoodsList', {
        title: goodsSearchName.value,
        page: goodsPage.value,
        size: 10
    }).then((res) => {
        if(res.data.code == 200) {
            goodsList.length = 0
            goodsList.push(...res.data.data)
            goodsPageCount.value = Math.ceil(res.data.total / 10)
        }
    })
}

// 添加物品到NPC
const addGoodsToNpc = (goods: GoodsItem) => {
    addGoodsDetail.value = {
        goodId: goods.id,
        goodName: goods.name,
        price: goods.price
    }
    showAddGoodsModal.value = false
    // 这里可以直接保存或者打开价格设置弹窗
    saveNpcGoods()
}

// 保存NPC物品
const saveNpcGoods = () => {
    $api.post('/admin/addNpcGoods', {
        npcId: currentNpcId.value,
        goodName: addGoodsDetail.value.goodName,
    }).then((res) => {
        if(res.data.code == 200) {
            window.$message.success('添加成功')
            getNpcGoods(currentNpcId.value)
        } else {
            window.$message.error(res.data.msg)
        }
    })
}

// 删除NPC物品
const deleteNpcGoods = (id: number) => {
    $api.post('/admin/delNpcGoods', { npcId:currentNpcId.value,goodId:id }).then((res) => {
        if(res.data.code == 200) {
            window.$message.success('删除成功')
            getNpcGoods(currentNpcId.value)
        } else {
            window.$message.error('删除失败')
        }
    })
}

// 搜索物品
const searchGoods = () => {
    goodsPage.value = 1
    getGoodsList()
}
const route = useRoute();
onMounted(() => {
    let id = route.query.mapId;
    if(id){
        mapId.value=Number(id)
    }
    getData()
});
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>
