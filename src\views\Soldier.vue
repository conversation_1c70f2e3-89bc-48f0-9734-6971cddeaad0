<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="角色id：">
                    <n-input-number v-model:value="mapId" clearable placeholder="输入角色id：" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <h2>士兵列表 
            <n-button type="primary" @click="showModal=true" style="margin-left: 20px;">添加</n-button>
            <n-button @click="toGenerayList" style="margin-left: 20px;">返回武将</n-button>
        </h2>
        <div class="contentTable">
            <n-data-table :columns="columns" :data="dataList" />
        </div>
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px;max-height: 80vh;overflow-y: auto;" title="添加/编辑npc" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="名称">
                    <n-input placeholder="请输入名称" v-model:value="generalDetail.name">
                    </n-input>
                </n-form-item>
                <n-form-item label="等级">
                    <n-input-number v-model:value="generalDetail.level"/>
                </n-form-item>
                <n-form-item label="攻击">
                    <n-input-number v-model:value="generalDetail.attack"/>
                </n-form-item>
                <n-form-item label="防御">
                    <n-input-number v-model:value="generalDetail.defense"/>
                </n-form-item>
                <n-form-item label="血量">
                    <n-input-number v-model:value="generalDetail.hp"/>
                </n-form-item>
                <n-form-item label="总数量">
                    <n-input-number v-model:value="generalDetail.count"/>
                </n-form-item>
                <n-form-item label="占用人口">
                    <n-input-number v-model:value="generalDetail.population"/>
                </n-form-item>
                <n-form-item label="兵器">
                    <n-input type="textarea" v-model:value="generalDetail.wuqiStr"/>
                </n-form-item>
                <n-form-item label="防具">
                    <n-input  type="textarea" v-model:value="generalDetail.fangjuStr"/>
                </n-form-item>
                <n-form-item label="技能名称">
                    <n-input placeholder="请输入技能名称" v-model:value="generalDetail.skillStr">
                    </n-input>
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="saveMsg()">保存</n-button></div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import router from '../router';
const $api = inject('$api') as AxiosInstance
const changeSelectedKey=inject('changeSelectedKey') as Function
let mapId = ref(1)
let showModal = ref(false)
let generalDetail = ref({
    id:undefined as number|undefined,
    name:'',
    level:undefined as number|undefined,
    hp:undefined as number|undefined,
    attack:undefined as number|undefined,
    defense:undefined as number|undefined,
    wuqiStr:'',
    fangjuStr:'',
    skillStr:'',
    count:undefined as number|undefined,
    population:undefined as number|undefined,
    generalId:undefined as number|undefined
})
interface NpcItem {
    id: number
    name: string
    level: number
    hp: number
    attack: number
    defense: number
    count: number
    population: number
    wuqiStr: string
    generalId:number
    fangjuStr: string
    skillStr: string
}
//跳转武将页面
const toGenerayList = () => {
    changeSelectedKey('general')
    router.push({ path: '/general', query: { id:mapId.value } })
}
let dataList: NpcItem[] = reactive([
    // { id: 1, name: 'npc', desc: 'npc的描述', directAttack: 0, canAttack: 0, canBuy: 0, gpsName: 1, intervalTime: 0 },
])
let columns = ref([
    {
        title: 'id',
        key: 'id'
    },
    {
        title: '名称',
        key: 'name'
    },
    {
        title: '等级',
        key: 'level',
    },
    {
        title: '攻击',
        key: 'attack',
    },
    {
        title: '防御',
        key: 'defense',
    },
    {
        title: '总数量',
        key: 'count',
    },
    {
        title: '占用人口',
        key: 'population',
    },
    {
        title: '兵器',
        key: 'wuqiStr',
    },
    {
        title: '防具',
        key: 'fangjuStr',
        width: 200,
        ellipsis: true
    },
    {
        title: '技能名称',
        key: 'skillStr',
    },
    {
        title: '操作',
        key: 'desc',
        fixed: 'right',
        width: 300,
        render(row: NpcItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            showModal.value = true
                            generalDetail.value = row
                            generalDetail.value.generalId=mapId.value
                        },
                    },
                    { default: () => '编辑' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            delSoldier(row.id)
                        },
                    },
                    { default: () => '删除' }
                )
            ])
        },
    },
])
const searchBtn = () => {
    getData()
}
const getData = () => {
    $api.post('/admin/getSoldier', { id:mapId.value }).then((res) => {
        if(res.data.code==200){
            dataList.length = 0
            dataList.push(...res.data.data)
            generalDetail.value={
                id:undefined as number|undefined,
                name:'',
                level:undefined as number|undefined,
                hp:undefined as number|undefined,
                attack:undefined as number|undefined,
                defense:undefined as number|undefined,
                wuqiStr:'',
                fangjuStr:'',
                skillStr:'',
                count:undefined as number|undefined,
                population:undefined as number|undefined,
                generalId:undefined as number|undefined
            }
            generalDetail.value.generalId=mapId.value
        }
        
    })
}
let saveMsg = () => {
    $api.post('/admin/addSoldier', {...generalDetail.value}).then((res) => {
        if(res.data.code==200){
            showModal.value = false
            window.$message.success('操作成功')
            getData()
        }else{

        }
    })
}
let delSoldier = (id: number) => {
    $api.post('/admin/delSoldier', { id: id }).then((res) => {
        if(res.data.code==200){
            window.$message.success('操作成功')
            getData()
        }
    })
}
const route = useRoute();
onMounted(() => {
    let id = route.query.id;
    if(id){
        mapId.value=Number(id)
    }
    getData()
});
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>