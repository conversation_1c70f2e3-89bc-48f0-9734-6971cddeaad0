<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item :label="skillTitle+'id：'">
                    <n-input-number v-model:value="skillId" clearable :placeholder="'输入'+skillTitle+'id：'" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <h2>技能列表</h2>
        <div class="contentTable">
            <n-data-table :columns="columns" :data="dataList" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton } from 'naive-ui';
import { computed, h, inject, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

const $api = inject('$api') as AxiosInstance
let skillId = ref(1)
let  skillType= ref('')
interface NpcItem {
    id: number
    name: string
    desc: string
    directAttack: number
    canAttack: number
    canBuy: number
    gpsName: number
    intervalTime: number
}
let dataList: NpcItem[] = reactive([
    // { id: 1, name: 'npc', desc: 'npc的描述', directAttack: 0, canAttack: 0, canBuy: 0, gpsName: 1, intervalTime: 0 },
])
let columns = ref([
    {
        title: 'id',
        key: 'id'
    },
    {
        title: '名称',
        key: 'name'
    },
    {
        title: '描述',
        key: 'desc',
        width: 200,
        ellipsis: true
    },
    {
        title: '直接攻击',
        key: 'directAttack',
        render(row: NpcItem) {
            return h('span', {}, row.directAttack ? '是' : '否')
        }
    },
    {
        title: '可攻击',
        key: 'canAttack',
        render(row: NpcItem) {
            return h('span', {}, row.canAttack ? '是' : '否')
        }
    },
    {
        title: '可购买',
        key: 'canBuy',
        render(row: NpcItem) {
            return h('span', {}, row.canBuy ? '是' : '否')
        }
    },
    {
        title: '地图id',
        key: 'gpsId'
    },
    {
        title: '杀死间隔',
        key: 'intervalTime'
    },
    {
        title: '操作',
        key: 'desc',
        fixed: 'right',
        width: 300,
        render(row: NpcItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            console.log('row', row);
                        },
                    },
                    { default: () => '编辑' }
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            console.log('row', row);
                        },
                    },
                    { default: () => '删除' }
                )
            ])
        },
    },
])
const searchBtn = () => {
    getData()
}
const getData = () => {
    let params={type:"",id:1}
    let url='/admin/getGeneralSkill'
    if(skillType.value){
        if(skillType.value=='soldier')url='/admin/getSoldierSkill'
        params.id=skillId.value
    }
    $api.post(url, params).then((res) => {
        if(res.data.code==200){
            dataList.length = 0
            dataList.push(...res.data.data)
        }
    })
}
const route = useRoute();
//写一个计算属性 skillTitle
let skillTitle=computed(()=>{
    return skillType.value=='soldier'?'士兵':'武将'
})
onMounted(() => {
    let id = route.query.id;
    let type=route.query.type
    if(id&&type){
        skillId.value=Number(id)
        skillType.value=String(type)
    }
    getData()
});
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>