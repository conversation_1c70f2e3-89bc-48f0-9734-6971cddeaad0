<template>
    <div class="container">
        <div class="search">
            <n-form inline label-width="auto" label-placement="left">
                <n-form-item label="物品名称：">
                    <n-input v-model:value="skillName" clearable placeholder="输入物品名称：" />
                </n-form-item>
                <n-form-item>
                    <n-button attr-type="button" @click="searchBtn">
                        搜索
                    </n-button>
                </n-form-item>
            </n-form>
        </div>
        <n-divider />
        <div>
            <h2>
                物品列表
                <n-button type="primary" @click="showModal = true" style="margin-left: 20px;">添加</n-button>
            </h2>

        </div>
        <div class="contentTable">
            <n-data-table :columns="columns" :data="dataList" />
            <div style="margin: 10px;display: flex;justify-content: center;">
                <n-pagination v-model:page="page" :page-count="pageCount" :on-update:page="getData" />
            </div>
        </div>
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" title="添加/编辑物品" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="物品名称">
                    <n-input placeholder="请输入技能名称" v-model:value="skillDetail.name">
                    </n-input>
                </n-form-item>
                <n-form-item label="物品描述">
                    <n-input placeholder="请输入物品描述" v-model:value="skillDetail.desc">
                    </n-input>
                </n-form-item>
                <n-form-item label="物品价格">
                    <n-input-number placeholder="请输入物品价格" v-model:value="skillDetail.price"/>
                </n-form-item>
                <n-form-item label="主分类">
                    <n-select v-model:value="skillDetail.type" placeholder="请选择主分类" :options="optionsType">
                    </n-select>
                </n-form-item>
                <n-form-item label="副分类">
                    <n-select v-model:value="skillDetail.subType" placeholder="请选择副分类" :options="optionsSubType">
                    </n-select>
                </n-form-item>
                <n-form-item label="抵消伤害/增加生命   ">
                    <n-input placeholder="请输入抵消伤害/增加生命" v-model:value="skillDetail.addhp">
                    </n-input>
                </n-form-item>
                <n-form-item label="增加攻击">
                    <n-input placeholder="" v-model:value="skillDetail.addatk">
                    </n-input>
                </n-form-item>
                <n-form-item label="增加防御">
                    <n-input placeholder="" v-model:value="skillDetail.adddef">
                    </n-input>
                </n-form-item>
                <n-form-item label="重量">
                    <n-input-number placeholder="" v-model:value="skillDetail.weight"/>
                </n-form-item>
                <n-form-item label="使用等级">
                    <n-input placeholder="" v-model:value="skillDetail.useLevel">
                    </n-input>
                </n-form-item>
                <n-form-item label="是否可锻造">
                    <n-select v-model:value="skillDetail.isForge" placeholder="" :options="optionsBase">
                    </n-select>
                </n-form-item>
                <n-form-item label="成功率">
                    <n-input placeholder="" v-model:value="skillDetail.successRate">
                    </n-input>
                </n-form-item>
                <n-form-item label="石头数量">
                    <n-input placeholder="" v-model:value="skillDetail.forgeMaterialCount">
                    </n-input>
                </n-form-item>
                <n-form-item label="100%石头数量">
                    <n-input placeholder="" v-model:value="skillDetail.forgeMaterialCount100">
                    </n-input>
                </n-form-item>
                <n-form-item label="玩家使用">
                    <n-select v-model:value="skillDetail.roleUse" placeholder="" :options="optionsBase">
                    </n-select>
                </n-form-item>
                <n-form-item label="武将使用">
                    <n-select v-model:value="skillDetail.generalUse" placeholder="" :options="optionsBase">
                    </n-select>
                </n-form-item>
                <n-form-item label="士兵使用">
                    <n-select v-model:value="skillDetail.soldierUse" placeholder="" :options="optionsBase">
                    </n-select>
                </n-form-item>
                <n-form-item label="战斗使用">
                    <n-select v-model:value="skillDetail.fightUse" placeholder="" :options="optionsBase">
                    </n-select>
                </n-form-item>
                <n-form-item label="绑定">
                    <n-select v-model:value="skillDetail.bind" placeholder="" :options="optionsBase1">
                    </n-select>
                </n-form-item>
                <n-form-item label="可叠加">
                    <n-select v-model:value="skillDetail.isStack" placeholder="" :options="optionsBase1">
                    </n-select>
                </n-form-item>
                <n-form-item label="初始孔数">
                    <n-input-number placeholder="请输入初始孔数" v-model:value="skillDetail.holeCount" :min="0"/>
                </n-form-item>
                <n-form-item label="打开物品">
                    <n-input placeholder="请输入打开后获得的物品" v-model:value="skillDetail.openGoods">
                    </n-input>
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="saveMsg()">保存</n-button></div>
            </template>
        </n-card>
    </n-modal>
    <n-modal v-model:show="showModalGood">
        <n-card style="width: 600px" title="赠送物品" :bordered="false" size="huge" role="dialog" aria-modal="true"
            :segmented="{ content: true }">
            <div>
                <n-form-item label="物品名称">
                    <n-input placeholder="请输入技能名称" v-model:value="sendGood.goodName" disabled>
                    </n-input>
                </n-form-item>
                <n-form-item label="玩家游戏名称">
                    <n-input placeholder="请输入玩家游戏名称" v-model:value="sendGood.userName">
                    </n-input>
                </n-form-item>
                <n-form-item label="赠送数量">
                    <n-input-number placeholder="请输入赠送数量" v-model:value="sendGood.num"/>
                </n-form-item>
            </div>
            <template #action>
                <div class="flex-center"><n-button @click="sendFn()">赠送</n-button></div>
            </template>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { AxiosInstance } from 'axios';
import { NButton} from 'naive-ui';
import { h, inject, onMounted, reactive, ref } from 'vue';

const $api = inject('$api') as AxiosInstance
let skillName = ref('')
let page = ref(1)
let pageCount = ref(1)
let showModalGood=ref(false)
let sendGood=reactive({
    goodId:1,
    goodName:'',
    userName:'',
    num:1
})
interface NpcItem {
    id: number;
    name: string;
    desc: string;
    price: number;
    type: number;
    subType: number;
    addhp: number;
    addatk: number;
    adddef: number;
    bind: 1 | 2;           // 1不绑定 2绑定
    weight: number;
    useLevel: number;
    roleUse: 0 | 1;        // 0不能使用 1可以使用
    generalUse: 0 | 1;
    soldierUse: 0 | 1;
    fightUse: 0 | 1;       // 1是只能战斗中使用
    isStack: 1 | 2;        // 1可叠加 2不可叠加
    isForge: 0 | 1;        // 是否可锻造
    successRate: number;   // 成功率
    forgeMaterialCount: number;     // 石头数量
    forgeMaterialCount100: number;  // 100%石头数量
    holeCount: number;     // 初始孔数
    openGoods:string;
}
let showModal = ref(false)
let dataList: NpcItem[] = reactive([
    // { id: 1, name: 'npc', desc: 'npc的描述', directAttack: 0, canAttack: 0, canBuy: 0, gpsName: 1, intervalTime: 0 },
])
let optionsType=[
    { label: '消耗品', value: 1 },
    { label: '兵器', value: 2 },
    { label: '防具', value: 3 },
    { label: '士兵兵器', value: 4 },
    { label: '士兵防具', value: 5 },
    { label: '坐骑装备', value: 6 },
    { label: '装备宝石', value: 7 },
    { label: '其他', value: 8 }
]
let optionsSubType = [
    { label: '刀', value: 21 },
    { label: '剑', value: 22 },
    { label: '棍', value: 23 },
    { label: '斧', value: 24 },
    { label: '锤', value: 25 },
    { label: '枪', value: 26 },
    { label: '帽子', value: 31 },
    { label: '衣服', value: 32 },
    { label: '裤子', value: 33 },
    { label: '鞋子', value: 34 },
    { label: '项链', value: 35 },
    { label: '戒指', value: 36 },
    { label: '手套', value: 37 },
    { label: '肩甲', value: 38 },
    { label: '披风', value: 39 },
    { label: '特殊戒指', value: 40 },
    { label: '左手套', value: 41 },
    { label: 'PK盾', value: 42 },
    { label: '蹄铁', value: 61 },
    { label: '马鞍', value: 62 },
    { label: '缰绳', value: 63 },
    { label: '马铠', value: 64 },
    { label: '马蹬', value: 65 },
    { label: '马嚼', value: 66 },
    { label: '商城', value: 71 },
    { label: '强化', value: 72 },
    { label: '材料', value: 73 },
    { label: '训练', value: 74 },
    { label: '任务', value: 75 },
    { label: '活动', value: 76 },
    { label: '神兵', value: 77 },
    { label: '其它', value: 78 },
]
let optionsBase=[
    { label: '否', value: 0 },
    { label: '是', value: 1 },
]
let optionsBase1=[
    { label: '否', value: 1 },
    { label: '是', value: 2 },
]
let skillDetail = ref({
    id: 0,
    name: '',
    desc: '',
    price: 0,
    type: 8,
    subType: 78,
    addhp: 0,
    addatk: 0,
    adddef: 0,
    bind: 1,
    weight: 1,
    useLevel: 0,
    roleUse: 0,
    generalUse: 0,
    soldierUse: 0,
    fightUse: 0,
    isStack: 2,
    isForge: 0,
    successRate: 0,
    forgeMaterialCount: 0,
    forgeMaterialCount100: 0,
    holeCount: 0,
    openGoods: ''
})
let columns = ref([
    {
        title: 'id',
        key: 'id'
    },
    {
        title: '名称',
        key: 'name',
        width: 100,
    },

    {
        title: '价格',
        key: 'price'
    },
    {
        title: '主类别',
        key: 'type',
        //1:消耗品 2:兵器 3:防具 4:士兵兵器 5:士兵防具 6:坐骑装备 7:装备宝石 8:其他 
        render(row: NpcItem) {
            let obj = {
                1: '消耗品',
                2: '兵器',
                3: '防具',
                4: '士兵兵器',
                5: '士兵防具',
                6: '坐骑装备',
                7: '装备宝石',
                8: '其他'
            };
            return obj[row.type as keyof typeof obj]
        }
    },
    {
        title: '副类别',
        key: 'subType',
        //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
        //31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
        //61:蹄铁 62:马鞍 63:缰绳 64:马铠 65:马蹬 66:马嚼
        //71:商城 72:强化 73:材料 74:训练 75:任务 76:活动 77:神兵 
        render(row: NpcItem) {
            let obj = {
                21: '刀',
                22: '剑',
                23: '棍',
                24: '斧',
                25: '锤',
                26: '枪',
                31: '帽子',
                32: '衣服',
                33: '裤子',
                34: '鞋子',
                35: '项链',
                36: '戒指',
                37: '手套',
                38: '肩甲',
                39: '披风',
                40: '特殊戒指',
                41: '左手套',
                42: 'PK盾',
                61: '蹄铁',
                62: '马鞍',
                63: '缰绳',
                64: '马铠',
                65: '马蹬',
                66: '马嚼',
                71: '商城',
                72: '强化',
                73: '材料',
                74: '训练',
                75: '任务',
                76: '活动',
                77: '神兵',
                78: '其它'
            };
            return obj[row.subType as keyof typeof obj]
        }
    },
    {
        title: '抵消伤害/增加生命',
        key: 'addhp'
    },
    {
        title: '增加攻击',
        key: 'addatk'

    },
    {
        title: '增加防御',
        key: 'adddef'
    },
    {
        title: '重量',
        key: 'weight'
    },
    {
        title: '使用等级',
        key: 'useLevel'
    },
    {
        title: '是否可锻造',
        key: 'isForge',
        render(row: NpcItem) {
            let obj = {
                0: '否',
                1: '是',
            }
            return obj[row.isForge as keyof typeof obj]
        }
    },
    {
        title: '成功率',
        key: 'successRate'
    },
    {
        title: '石头数量',
        key: 'forgeMaterialCount'
    },
    {
        title: '100%石头数量',
        key: 'forgeMaterialCount100'
    },
    {
        title: '玩家使用',
        key: 'roleUse',
        render(row: NpcItem) {
            let obj = {
                0: '否',
                1: '是',
            }
            return obj[row.roleUse as keyof typeof obj]
        }
    },
    {
        title: '武将使用',
        key: 'generalUse',
        render(row: NpcItem) {
            let obj = {
                0: '否',
                1: '是',
            }
            return obj[row.generalUse as keyof typeof obj]
        }
    },
    {
        title: '士兵使用',
        key: 'soldierUse',
        render(row: NpcItem) {
            let obj = {
                0: '否',
                1: '是',
            }
            return obj[row.soldierUse as keyof typeof obj]
        }
    },
    {
        title: '战斗使用',
        key: 'fightUse',
        render(row: NpcItem) {
            let obj = {
                0: '否',
                1: '是',
            }
            return obj[row.fightUse as keyof typeof obj]
        }
    },
    {
        title: '绑定',
        key: 'bind',
        //1不绑定 2绑定
        render(row: NpcItem) {
            let obj = {
                1: '不绑定',
                2: '绑定',
            }
            return obj[row.bind as keyof typeof obj]
        }
    },
    {
        title: '叠加',
        key: 'isStack',
        //1叠加 2不可叠加
        render(row: NpcItem) {
            let obj = {
                1: '不可叠加',
                2: '叠加',
            }
            return obj[row.isStack as keyof typeof obj]
        }
    },
    {
        title: '初始孔数',
        key: 'holeCount',
        width: 100
    },
    {
        title: '描述',
        key: 'desc',
        width: 100,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '打开物品',
        key: 'openGoods',
        width: 100,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '操作',
        key: 'desc',
        fixed: 'right',
        width: 250,
        render(row: NpcItem) {
            return h('div', {}, [
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            showModal.value = true
                            skillDetail.value = row
                        },
                    },
                    '编辑'
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            showModalGood.value = true
                            sendGood.goodId=row.id
                            sendGood.goodName=row.name
                        },
                    },
                    '赠送物品'
                ),
                h(
                    NButton,
                    {
                        size: 'small',
                        type: 'info',
                        onClick: () => {
                            delSkill(row.id)
                        },
                    },
                    '删除'
                )
            ])
        },
    },
])
const searchBtn = () => {
    page.value = 1
    getData()
}
const getData = (newpage?: number) => {
    newpage && (page.value = newpage)
    $api.post('/admin/getGoodsList', { title: skillName.value, page: page.value, size: 10 }).then((res) => {
        if (res.data.code == 200) {
            dataList.length = 0
            dataList.push(...res.data.data)
            pageCount.value = Math.ceil(res.data.total / 10)
        }
    })
}
const saveMsg = () => {
    $api.post('/admin/addGood', skillDetail.value).then((res) => {
        if (res.data.code == 200) {
            showModal.value = false
            window.$message.success('操作成功')
            getData()
        } else {

        }
    })
}
const sendFn=()=>{
    $api.post('/admin/sendGood', sendGood).then((res) => {
        if (res.data.code == 200) {
            showModalGood.value = false
            window.$message.success('赠送成功')
        } else {
            window.$message.error('出错了')
        }
    })
}
const delSkill = (id: number) => {
    $api.post('/admin/delGood', { id }).then((res) => {
        if (res.data.code == 200) {
            window.$message.success('删除成功')
            getData()
        }
    })
}
onMounted(() => {
    getData()
});
</script>

<style scoped>
.container {
    padding: 30px;
    box-sizing: border-box;
}
</style>