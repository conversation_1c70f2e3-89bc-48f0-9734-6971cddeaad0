// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
    {
        path: '/',
        name: 'Map',
        component: () => import('../views/Map.vue')
    },
    {
        path: '/mapInfo',
        name: 'MapInfo',
        component: () => import('../views/MapInfo.vue')
    },
    {
        path: '/npc',
        name: 'Npc',
        component: () => import('../views/Npc.vue'),
    },
    {
        path: '/general',
        name: 'General',
        component: () => import('../views/General.vue'),
    },
    {
        path: '/drop',
        name: 'Drop',
        component: () => import('../views/Drop.vue'),
    },
    {
        path: '/soldier',
        name: 'Soldier',
        component: () => import('../views/Soldier.vue'),
    },
    {
        path: '/skill',
        name: 'Skill',
        component: () => import('../views/Skill.vue'),
    },
    {
        path: '/skillTemplate',
        name: 'SkillTemplate',
        component: () => import('../views/SkillTemplate.vue'),
    },
    {
        path: '/goodsTemplate',
        name: 'GoodsTemplate',
        component: () => import('../views/GoodsTemplate.vue'),
    },
    {
        path: '/moniqi',
        name: 'Moniqi',
        component: () => import('../views/Moniqi.vue'),
    },
    {
        path: '/goodsRequirement',
        name: 'GoodsRequirement',
        component: () => import('../views/GoodsRequirement.vue'),
    },
];
const router = createRouter({
    history: createWebHistory(),
    routes
});
export default router;
